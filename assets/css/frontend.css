/* CFB Calculator Frontend Styles */

/* Reset and Base Styles */
.cfb-calculator-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 800px;
    margin: 0 auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.cfb-calculator-wrapper:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* RTL Support */
.cfb-calculator-wrapper[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

.cfb-calculator-wrapper[dir="rtl"] .cfb-field-label {
    text-align: right;
}

/* Header */
.cfb-calculator-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    margin: 0;
    padding: 24px 30px;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
}

.cfb-calculator-description {
    padding: 20px 30px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    color: #6c757d;
    text-align: center;
}

/* Form Styles */
.cfb-calculator-form {
    padding: 30px;
}

.cfb-form-fields {
    display: grid;
    gap: 24px;
    margin-bottom: 30px;
    grid-template-columns: repeat(12, 1fr);
}

/* Field Styles */
.cfb-field {
    position: relative;
    transition: all 0.3s ease;
    grid-column: span 12; /* Default full width */
}

/* Field Width Classes */
.cfb-field.cfb-field-width-full {
    grid-column: span 12 !important;
    width: 100% !important;
}

.cfb-field.cfb-field-width-half {
    grid-column: span 6 !important;
    width: 100% !important;
}

.cfb-field.cfb-field-width-third {
    grid-column: span 4 !important;
    width: calc(33.333% - 16px) !important;
}

.cfb-field.cfb-field-width-quarter {
    grid-column: span 3 !important;
    width: calc(25% - 18px) !important;
}

/* Responsive adjustments for field widths */
@media (max-width: 768px) {
    .cfb-field.cfb-field-width-half,
    .cfb-field.cfb-field-width-third,
    .cfb-field.cfb-field-width-quarter {
        grid-column: span 12 !important;
        width: 100% !important;
    }
}

.cfb-field.cfb-field-hidden {
    display: none;
}

.cfb-field-label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
    font-size: 14px;
}

.cfb-required {
    color: #e74c3c;
    margin-left: 4px;
}

.cfb-field-input {
    position: relative;
}

/* Input Styles */
.cfb-text-input,
.cfb-number-input,
.cfb-select-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fff;
    box-sizing: border-box;
}

.cfb-text-input:focus,
.cfb-number-input:focus,
.cfb-select-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Slider Styles */
.cfb-slider-container {
    padding: 10px 0;
}

.cfb-slider-input {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
}

.cfb-slider-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.cfb-slider-input::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

.cfb-slider-input::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.cfb-slider-value {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 14px;
    color: #6c757d;
}

.cfb-slider-current {
    font-weight: 600;
    color: #667eea;
    font-size: 16px;
}

/* Radio and Checkbox Styles */
.cfb-radio-group,
.cfb-checkbox-group {
    display: grid;
    gap: 12px;
}

.cfb-radio-option,
.cfb-checkbox-option {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
}

.cfb-radio-option:hover,
.cfb-checkbox-option:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.cfb-radio-option input,
.cfb-checkbox-option input {
    margin-right: 12px;
    margin-left: 0;
    transform: scale(1.2);
    accent-color: #667eea;
}

.cfb-radio-option input:checked + .cfb-radio-label,
.cfb-checkbox-option input:checked + .cfb-checkbox-label {
    color: #667eea;
    font-weight: 600;
}

.cfb-option-price {
    margin-left: auto;
    color: #28a745;
    font-weight: 600;
}

/* RTL adjustments for radio/checkbox */
.cfb-calculator-wrapper[dir="rtl"] .cfb-radio-option input,
.cfb-calculator-wrapper[dir="rtl"] .cfb-checkbox-option input {
    margin-right: 0;
    margin-left: 12px;
}

.cfb-calculator-wrapper[dir="rtl"] .cfb-option-price {
    margin-left: 0;
    margin-right: auto;
}

/* Field Description */
.cfb-field-description {
    margin-top: 6px;
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
}

/* Calculation Results */
.cfb-calculation-results {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid #e9ecef;
}

.cfb-subtotals h4 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.cfb-subtotal-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.cfb-subtotal-line:last-child {
    border-bottom: none;
}

.cfb-subtotal-label {
    color: #495057;
    font-size: 14px;
}

.cfb-subtotal-value {
    font-weight: 600;
    color: #28a745;
    font-size: 14px;
}

.cfb-total-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid #667eea;
}

.cfb-total-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
}

.cfb-total-label {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
}

.cfb-total-value {
    font-size: 24px;
    font-weight: 700;
    color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Form Actions */
.cfb-form-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 24px;
}

.cfb-calculate-btn,
.cfb-reset-btn {
    padding: 14px 28px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cfb-calculate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.cfb-calculate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.cfb-reset-btn {
    background: #6c757d;
    color: #fff;
}

.cfb-reset-btn:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Loading State */
.cfb-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 20px;
    color: #667eea;
    font-weight: 600;
}

.cfb-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: cfb-spin 1s linear infinite;
}

@keyframes cfb-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message */
.cfb-error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #f5c6cb;
    margin-top: 16px;
    font-size: 14px;
}

/* Success Message */
.cfb-success-message {
    background: #d1e7dd;
    color: #0f5132;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #badbcc;
    margin-top: 16px;
    font-size: 14px;
}

/* Invoice Section */
.cfb-invoice-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin-top: 24px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.cfb-invoice-checkbox {
    margin-bottom: 20px;
}

.cfb-invoice-checkbox label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
}

.cfb-invoice-checkbox input[type="checkbox"] {
    margin-right: 12px;
    transform: scale(1.3);
    accent-color: #667eea;
}

.cfb-invoice-form {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    border: 1px solid #e9ecef;
    margin-top: 16px;
}

.cfb-invoice-form h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding-bottom: 12px;
    border-bottom: 2px solid #667eea;
}

.cfb-invoice-fields {
    display: grid;
    gap: 20px;
}

.cfb-field-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.cfb-field-col {
    display: flex;
    flex-direction: column;
}

.cfb-field-col label {
    font-weight: 600;
    margin-bottom: 6px;
    color: #2c3e50;
    font-size: 14px;
}

.cfb-field-col input,
.cfb-field-col textarea {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fff;
    box-sizing: border-box;
}

.cfb-field-col input:focus,
.cfb-field-col textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.cfb-field-col textarea {
    resize: vertical;
    min-height: 80px;
}

.cfb-invoice-actions {
    text-align: center;
    margin-top: 20px;
}

.cfb-generate-invoice-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: #fff;
    border: none;
    padding: 14px 28px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.cfb-generate-invoice-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.cfb-generate-invoice-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

/* RTL adjustments for invoice section */
.cfb-calculator-wrapper[dir="rtl"] .cfb-invoice-checkbox input[type="checkbox"] {
    margin-right: 0;
    margin-left: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cfb-calculator-wrapper {
        margin: 0 16px;
        border-radius: 8px;
    }

    .cfb-calculator-form {
        padding: 20px;
    }

    .cfb-calculator-title {
        padding: 20px;
        font-size: 20px;
    }

    .cfb-form-actions {
        flex-direction: column;
    }

    .cfb-calculate-btn,
    .cfb-reset-btn {
        width: 100%;
    }

    .cfb-total-value {
        font-size: 20px;
    }

    /* Responsive field widths - all fields become full width on mobile */
    .cfb-field.cfb-field-width-half,
    .cfb-field.cfb-field-width-third,
    .cfb-field.cfb-field-width-quarter {
        grid-column: span 12;
    }
    
    .cfb-radio-group,
    .cfb-checkbox-group {
        gap: 8px;
    }
    
    .cfb-radio-option,
    .cfb-checkbox-option {
        padding: 10px 12px;
    }

    /* Invoice section responsive */
    .cfb-field-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .cfb-invoice-form {
        padding: 16px;
    }

    .cfb-generate-invoice-btn {
        width: 100%;
        padding: 12px 20px;
        font-size: 14px;
    }
}

/* Animation Classes */
.cfb-fade-in {
    animation: cfb-fadeIn 0.5s ease-in-out;
}

.cfb-slide-up {
    animation: cfb-slideUp 0.5s ease-in-out;
}

@keyframes cfb-fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes cfb-slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .cfb-calculator-wrapper {
        border: 2px solid #000;
    }
    
    .cfb-text-input,
    .cfb-number-input,
    .cfb-select-input {
        border: 2px solid #000;
    }
    
    .cfb-calculate-btn {
        background: #000;
        border: 2px solid #000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .cfb-calculator-wrapper,
    .cfb-field,
    .cfb-text-input,
    .cfb-number-input,
    .cfb-select-input,
    .cfb-slider-input::-webkit-slider-thumb,
    .cfb-radio-option,
    .cfb-checkbox-option,
    .cfb-calculate-btn,
    .cfb-reset-btn {
        transition: none;
    }
    
    .cfb-spinner {
        animation: none;
    }
    
    .cfb-fade-in,
    .cfb-slide-up {
        animation: none;
    }
}

/* Hide Results Initially */
.cfb-results-hidden .cfb-calculation-field,
.cfb-results-hidden .cfb-total-section,
.cfb-results-hidden .cfb-results-section,
.cfb-results-hidden .cfb-subtotal-section,
.cfb-results-hidden .cfb-calculation-results {
    display: none !important;
}

/* Show results with animation when revealed */
.cfb-calculation-field,
.cfb-total-section,
.cfb-results-section,
.cfb-subtotal-section,
.cfb-calculation-results {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-calculation-field,
.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-total-section,
.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-results-section,
.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-subtotal-section,
.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-calculation-results {
    opacity: 1;
    transform: translateY(0);
}

/* Conditional field hiding */
.cfb-field-hidden {
    display: none !important;
}

.cfb-field.cfb-fade-in {
    animation: cfbFadeIn 0.3s ease;
}

@keyframes cfbFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ========================================
   THEME STYLES
   ======================================== */

/* MATERIAL DESIGN THEME */
.cfb-theme-material {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08), 0 8px 32px rgba(0, 0, 0, 0.12);
    border-radius: 8px;
    overflow: hidden;
    background: #ffffff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cfb-theme-material .cfb-calculator-title {
    background: #6200ee;
    color: #ffffff;
    padding: 24px 32px;
    font-size: 24px;
    font-weight: 500;
    text-align: center;
    letter-spacing: 0.15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
}

.cfb-theme-material .cfb-calculator-description {
    background: #f5f5f5;
    padding: 24px 32px;
    color: #616161;
    font-size: 16px;
    line-height: 1.6;
    border-bottom: 1px solid #e0e0e0;
}

.cfb-theme-material .cfb-calculator-form {
    padding: 32px;
    background: #fafafa;
}

.cfb-theme-material .cfb-field-label {
    color: #424242;
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cfb-theme-material .cfb-text-input,
.cfb-theme-material .cfb-number-input,
.cfb-theme-material .cfb-select-input {
    border: none;
    border-bottom: 2px solid #e0e0e0;
    border-radius: 4px 4px 0 0;
    background: #ffffff;
    padding: 20px 16px 10px 16px;
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    position: relative;
    height: 56px;
    box-sizing: border-box;
}

.cfb-theme-material .cfb-text-input:focus,
.cfb-theme-material .cfb-number-input:focus,
.cfb-theme-material .cfb-select-input:focus {
    border-bottom-color: #6200ee;
    box-shadow: 0 2px 6px rgba(98, 0, 238, 0.15);
    outline: none;
}

.cfb-theme-material .cfb-field-label {
    color: #666666;
    font-weight: 500;
    font-size: 12px;
    margin-bottom: 8px;
    letter-spacing: 0.4px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    padding-left: 16px;
}

.cfb-theme-material .cfb-field:focus-within .cfb-field-label {
    color: #6200ee;
}

.cfb-theme-material .cfb-radio-option,
.cfb-theme-material .cfb-checkbox-option {
    background: #ffffff;
    border: none;
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cfb-theme-material .cfb-radio-option:hover,
.cfb-theme-material .cfb-checkbox-option:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    border-color: transparent;
    background: #ffffff;
}

.cfb-theme-material .cfb-calculate-btn {
    background: #6200ee;
    border-radius: 4px;
    padding: 12px 24px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1.25px;
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 14px;
    height: 36px;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.cfb-theme-material .cfb-calculate-btn:hover {
    background: #7722FF;
    box-shadow: 0 4px 8px rgba(98, 0, 238, 0.3);
    transform: translateY(-1px);
}

.cfb-theme-material .cfb-reset-btn {
    background: transparent;
    border: 1px solid #6200ee;
    color: #6200ee;
    border-radius: 4px;
    padding: 12px 24px;
    font-weight: 500;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1.25px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 14px;
    height: 36px;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
}

.cfb-theme-material .cfb-reset-btn:hover {
    background: rgba(98, 0, 238, 0.08);
    transform: translateY(-1px);
}

.cfb-theme-material .cfb-calculation-results {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e0e0e0;
    padding: 24px;
    margin-bottom: 24px;
}

.cfb-theme-material .cfb-total-value {
    color: #1976d2;
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cfb-theme-material .cfb-calculation-field {
    background: #ffffff;
    border-radius: 8px;
    padding: 16px 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;
    margin: 16px 0;
}

.cfb-theme-material .cfb-calculation-value {
    font-size: 18px;
    font-weight: 500;
    color: #1976d2;
}

.cfb-theme-material .cfb-slider-input {
    background: #e0e0e0;
    height: 6px;
}

.cfb-theme-material .cfb-slider-input::-webkit-slider-thumb {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    width: 20px;
    height: 20px;
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
}

/* MINIMAL THEME */
.cfb-theme-minimal {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: #ffffff;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    overflow: hidden;
    transition: all 0.2s ease;
}

.cfb-theme-minimal .cfb-calculator-title {
    background: #ffffff;
    color: #111111;
    padding: 24px 28px;
    font-size: 22px;
    font-weight: 600;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
    margin: 0;
    letter-spacing: -0.2px;
}

.cfb-theme-minimal .cfb-calculator-description {
    background: #fafafa;
    padding: 16px 28px;
    color: #666666;
    font-size: 14px;
    line-height: 1.5;
    border-bottom: 1px solid #f0f0f0;
    text-align: center;
    font-weight: 400;
}

.cfb-theme-minimal .cfb-calculator-form {
    padding: 28px;
    background: #ffffff;
}

.cfb-theme-minimal .cfb-field-label {
    color: #333333;
    font-weight: 500;
    font-size: 13px;
    margin-bottom: 8px;
    letter-spacing: 0;
}

.cfb-theme-minimal .cfb-text-input,
.cfb-theme-minimal .cfb-number-input,
.cfb-theme-minimal .cfb-select-input {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: #ffffff;
    padding: 10px 14px;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: none;
    height: 42px;
    box-sizing: border-box;
}

.cfb-theme-minimal .cfb-text-input:focus,
.cfb-theme-minimal .cfb-number-input:focus,
.cfb-theme-minimal .cfb-select-input:focus {
    border-color: #000000;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
    outline: none;
}

.cfb-theme-minimal .cfb-field:focus-within .cfb-field-label {
    color: #000000;
}

.cfb-theme-minimal .cfb-radio-option,
.cfb-theme-minimal .cfb-checkbox-option {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 10px 14px;
    margin-bottom: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.cfb-theme-minimal .cfb-radio-option:hover,
.cfb-theme-minimal .cfb-checkbox-option:hover {
    border-color: #000000;
    background: #fafafa;
}

.cfb-theme-minimal .cfb-radio-label,
.cfb-theme-minimal .cfb-checkbox-label {
    font-size: 14px;
    padding-left: 6px;
}

.cfb-theme-minimal .cfb-calculate-btn {
    background: #000000;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-weight: 500;
    font-size: 14px;
    text-transform: none;
    letter-spacing: 0;
    transition: all 0.2s ease;
    box-shadow: none;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.cfb-theme-minimal .cfb-calculate-btn:hover {
    background: #333333;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.cfb-theme-minimal .cfb-reset-btn {
    background: #ffffff;
    color: #666666;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 10px 20px;
    font-weight: 500;
    font-size: 14px;
    text-transform: none;
    letter-spacing: 0;
    transition: all 0.2s ease;
    box-shadow: none;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
}

.cfb-theme-minimal .cfb-reset-btn:hover {
    background: #f5f5f5;
    border-color: #000000;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cfb-theme-minimal .cfb-calculation-results {
    background: #fafafa;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    padding: 20px;
    margin-bottom: 20px;
}

.cfb-theme-minimal .cfb-total-value {
    color: #1a1a1a;
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
    font-weight: 600;
}

/* PROFESSIONAL CORPORATE THEME */
.cfb-theme-modern {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #ffffff;
    border: 1px solid #e0e6ed;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    position: relative;
}

.cfb-theme-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: #2c5282;
}

.cfb-theme-modern .cfb-calculator-title {
    background: #2c5282;
    color: #ffffff;
    padding: 24px 32px;
    font-size: 22px;
    font-weight: 600;
    text-align: center;
    letter-spacing: 0.2px;
    position: relative;
    margin-top: 0;
}

.cfb-theme-modern .cfb-calculator-description {
    background: #f8f9fa;
    padding: 16px 32px;
    color: #4a5568;
    font-size: 14px;
    line-height: 1.6;
    border-bottom: 1px solid #e2e8f0;
    text-align: center;
    font-weight: 400;
}

.cfb-theme-modern .cfb-calculator-form {
    padding: 28px 32px;
    background: #ffffff;
}

.cfb-theme-modern .cfb-field-label {
    color: #2d3748;
    font-weight: 600;
    font-size: 13px;
    margin-bottom: 8px;
    letter-spacing: 0.3px;
    position: relative;
}

.cfb-theme-modern .cfb-field-label::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 24px;
    height: 2px;
    background: #2c5282;
}

.cfb-theme-modern .cfb-text-input,
.cfb-theme-modern .cfb-number-input,
.cfb-theme-modern .cfb-select-input {
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background: #ffffff;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
    font-weight: 400;
    height: 46px;
    box-sizing: border-box;
}

.cfb-theme-modern .cfb-text-input:focus,
.cfb-theme-modern .cfb-number-input:focus,
.cfb-theme-modern .cfb-select-input:focus {
    border-color: #2c5282;
    box-shadow: 0 2px 6px rgba(44, 82, 130, 0.15);
    outline: none;
    transform: translateY(0);
}

.cfb-theme-modern .cfb-field:focus-within .cfb-field-label {
    color: #2c5282;
}

.cfb-theme-modern .cfb-radio-option,
.cfb-theme-modern .cfb-checkbox-option {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 12px 16px;
    margin-bottom: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.cfb-theme-modern .cfb-radio-option::before,
.cfb-theme-modern .cfb-checkbox-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: #2c5282;
    transform: scaleY(0);
    transition: transform 0.2s ease;
}

.cfb-theme-modern .cfb-radio-option:hover,
.cfb-theme-modern .cfb-checkbox-option:hover {
    border-color: #2c5282;
    box-shadow: 0 2px 6px rgba(44, 82, 130, 0.1);
    transform: translateY(-1px);
}

.cfb-theme-modern .cfb-radio-label,
.cfb-theme-modern .cfb-checkbox-label {
    font-size: 14px;
    padding-left: 6px;
}

.cfb-theme-modern .cfb-radio-option:hover::before,
.cfb-theme-modern .cfb-checkbox-option:hover::before {
    transform: scaleY(1);
}

.cfb-theme-modern .cfb-calculate-btn {
    background: #2c5282;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.3px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(44, 82, 130, 0.2);
    position: relative;
    overflow: hidden;
    height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.cfb-theme-modern .cfb-calculate-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    transition: left 0.4s ease;
}

.cfb-theme-modern .cfb-calculate-btn:hover {
    background: #3a689d;
    box-shadow: 0 3px 8px rgba(44, 82, 130, 0.3);
    transform: translateY(-1px);
}

.cfb-theme-modern .cfb-calculate-btn:hover::before {
    left: 100%;
}

.cfb-theme-modern .cfb-reset-btn {
    background: #ffffff;
    color: #2c5282;
    border: 1px solid #2c5282;
    border-radius: 4px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.3px;
    transition: all 0.2s ease;
    box-shadow: none;
    height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
}

.cfb-theme-modern .cfb-reset-btn:hover {
    background: rgba(44, 82, 130, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 1px 3px rgba(44, 82, 130, 0.1);
}

.cfb-theme-modern .cfb-calculation-results {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    margin-bottom: 20px;
}

.cfb-theme-modern .cfb-calculation-results::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: #2c5282;
    border-radius: 6px 6px 0 0;
}

.cfb-theme-modern .cfb-total-value {
    color: #007bff;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

/* ========================================
   THEME RESPONSIVE STYLES
   ======================================== */

@media (max-width: 768px) {
    /* Material Theme Mobile */
    .cfb-theme-material .cfb-calculator-title {
        padding: 24px 20px;
        font-size: 22px;
    }

    .cfb-theme-material .cfb-calculator-form {
        padding: 24px 20px;
    }

    .cfb-theme-material .cfb-calculate-btn,
    .cfb-theme-material .cfb-reset-btn {
        width: 100%;
        margin-bottom: 12px;
        padding: 14px 24px;
        border-radius: 20px;
    }

    /* Minimal Theme Mobile */
    .cfb-theme-minimal .cfb-calculator-title {
        padding: 20px 24px;
        font-size: 20px;
    }

    .cfb-theme-minimal .cfb-calculator-form {
        padding: 24px 20px;
    }

    .cfb-theme-minimal .cfb-calculate-btn,
    .cfb-theme-minimal .cfb-reset-btn {
        width: 100%;
        margin-bottom: 12px;
    }

    /* Modern Theme Mobile */
    .cfb-theme-modern .cfb-calculator-title {
        padding: 24px 20px;
        font-size: 22px;
    }

    .cfb-theme-modern .cfb-calculator-form {
        padding: 28px 20px;
    }

    .cfb-theme-modern .cfb-calculate-btn,
    .cfb-theme-modern .cfb-reset-btn {
        width: 100%;
        margin-bottom: 12px;
        padding: 14px 28px;
    }
}

/* ========================================
   RTL SUPPORT FOR THEMES
   ======================================== */

/* Material Theme RTL */
.cfb-theme-material[dir="rtl"] .cfb-field-label {
    text-align: right;
}

.cfb-theme-material[dir="rtl"] .cfb-text-input,
.cfb-theme-material[dir="rtl"] .cfb-number-input,
.cfb-theme-material[dir="rtl"] .cfb-select-input {
    text-align: right;
    direction: rtl;
}

/* Minimal Theme RTL */
.cfb-theme-minimal[dir="rtl"] .cfb-field-label {
    text-align: right;
}

.cfb-theme-minimal[dir="rtl"] .cfb-text-input,
.cfb-theme-minimal[dir="rtl"] .cfb-number-input,
.cfb-theme-minimal[dir="rtl"] .cfb-select-input {
    text-align: right;
    direction: rtl;
}

/* Modern Theme RTL */
.cfb-theme-modern[dir="rtl"] .cfb-field-label {
    text-align: right;
}

.cfb-theme-modern[dir="rtl"] .cfb-field-label::after {
    left: auto;
    right: 0;
}

.cfb-theme-modern[dir="rtl"] .cfb-text-input,
.cfb-theme-modern[dir="rtl"] .cfb-number-input,
.cfb-theme-modern[dir="rtl"] .cfb-select-input {
    text-align: right;
    direction: rtl;
}

.cfb-theme-modern[dir="rtl"] .cfb-radio-option::before,
.cfb-theme-modern[dir="rtl"] .cfb-checkbox-option::before {
    left: auto;
    right: 0;
}
